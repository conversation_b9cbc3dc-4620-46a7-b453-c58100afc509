import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';

// GET /api/photos?projectId=xxx
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const projectId = searchParams.get('projectId');
    
    console.log('API: Fetching photos', projectId ? `for project ${projectId}` : '');

    const photos = await prisma.photo.findMany({
      where: projectId ? { project_id: projectId } : {},
      orderBy: {
        created_at: 'desc',
      },
    });

    const photosData = photos.map((photo: any) => ({
      id: photo.id,
      title: photo.title,
      date: photo.created_at.toISOString().split('T')[0],
      thumbnail: photo.file_path ? `/uploads/photos/${photo.file_path}` : '',
      file_path: photo.file_path,
      project_id: photo.project_id,
      folder_id: photo.folder_id,
    }));

    return NextResponse.json({ data: photosData, error: null });
  } catch (error) {
    console.error('API Error in GET /api/photos:', error);
    return NextResponse.json({ data: null, error: error.message }, { status: 500 });
  }
}

// POST /api/photos
export async function POST(request: NextRequest) {
  try {
    const formData = await request.formData();
    const file = formData.get('file') as File;
    const projectId = formData.get('projectId') as string;
    const folderId = formData.get('folderId') as string;
    
    if (!file) {
      return NextResponse.json({ data: null, error: 'No file provided' }, { status: 400 });
    }

    console.log('API: Creating photo record:', file.name);

    // Extract title from filename (without extension)
    const title = file.name.split('.')[0];
    
    // Generate a unique file path
    const fileId = crypto.randomUUID();
    const fileExt = file.name.split('.').pop();
    const filePath = `${fileId}.${fileExt}`;

    // Save the actual file to the uploads directory
    const fs = require('fs');
    const path = require('path');

    // Create uploads directory if it doesn't exist
    const uploadsDir = path.join(process.cwd(), 'public', 'uploads', 'photos');
    if (!fs.existsSync(uploadsDir)) {
      fs.mkdirSync(uploadsDir, { recursive: true });
    }

    // Save the file
    const buffer = Buffer.from(await file.arrayBuffer());
    const fullFilePath = path.join(uploadsDir, filePath);
    fs.writeFileSync(fullFilePath, buffer);

    // Create photo record in database
    const photo = await prisma.photo.create({
      data: {
        title: title,
        file_path: filePath,
        file_size: file.size,
        mime_type: file.type,
        project_id: projectId || null,
        folder_id: folderId || null,
      },
    });

    const photoData = {
      id: photo.id,
      title: photo.title,
      date: photo.created_at.toISOString().split('T')[0],
      thumbnail: `/uploads/photos/${photo.file_path}`,
      file_path: photo.file_path,
      project_id: photo.project_id,
      folder_id: photo.folder_id,
    };

    return NextResponse.json({ data: photoData, error: null });
  } catch (error) {
    console.error('API Error in POST /api/photos:', error);
    return NextResponse.json({ data: null, error: error.message }, { status: 500 });
  }
}

// DELETE /api/photos?id=xxx
export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const id = searchParams.get('id');

    if (!id) {
      return NextResponse.json({ error: 'Photo ID is required' }, { status: 400 });
    }

    console.log('API: Deleting photo:', id);

    // Get photo from database
    const photo = await prisma.photo.findUnique({
      where: { id }
    });

    if (!photo) {
      return NextResponse.json({ error: 'Photo not found' }, { status: 404 });
    }

    // Delete file from filesystem
    const fs = require('fs');
    const path = require('path');
    const filePath = path.join(process.cwd(), 'public', 'uploads', 'photos', photo.file_path);

    try {
      if (fs.existsSync(filePath)) {
        fs.unlinkSync(filePath);
      }
    } catch (fileError) {
      console.warn('Warning: Could not delete file from disk:', fileError);
      // Continue with database deletion even if file deletion fails
    }

    // Delete photo record from database
    await prisma.photo.delete({
      where: { id }
    });

    return NextResponse.json({ success: true, error: null });
  } catch (error) {
    console.error('API Error in DELETE /api/photos:', error);
    return NextResponse.json({ success: false, error: error.message }, { status: 500 });
  }
}
