'use client';

import React, { useState } from 'react';
import { useUnsavedChanges } from '@/hooks/use-unsaved-changes';
import Link from 'next/link';

export default function TestUnsavedPage() {
  const [inputValue, setInputValue] = useState('');
  const [hasChanges, setHasChanges] = useState(false);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setInputValue(e.target.value);
    setHasChanges(true);
    console.log('Setting unsaved changes to true');
  };

  const handleSave = async () => {
    console.log('Saving changes...');
    // Simulate save
    await new Promise(resolve => setTimeout(resolve, 1000));
    setHasChanges(false);
    console.log('Changes saved, unsaved changes set to false');
  };

  // Use the unsaved changes hook
  const { checkUnsavedChanges, saveAndNavigate } = useUnsavedChanges({
    hasUnsavedChanges: hasChanges,
    onSave: handleSave,
    message: 'You have unsaved changes in the test form. Are you sure you want to leave?'
  });

  return (
    <div className="p-8">
      <h1 className="text-2xl font-bold mb-4">Test Unsaved Changes</h1>
      
      <div className="mb-4">
        <label className="block text-sm font-medium mb-2">
          Test Input (type something to trigger unsaved changes):
        </label>
        <input
          type="text"
          value={inputValue}
          onChange={handleInputChange}
          className="border border-gray-300 rounded px-3 py-2 w-full"
          placeholder="Type something here..."
        />
      </div>

      <div className="mb-4">
        <button
          onClick={handleSave}
          className="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700"
        >
          Save Changes
        </button>
      </div>

      <div className="space-y-2">
        <p>Try navigating away after typing something:</p>
        <Link href="/projects" className="text-blue-600 hover:underline block">
          Go to Projects (should show dialog if unsaved changes)
        </Link>
        <Link href="/" className="text-blue-600 hover:underline block">
          Go to Home (should show dialog if unsaved changes)
        </Link>
      </div>

      <div className="mt-8 p-4 bg-gray-100 rounded">
        <h3 className="font-semibold mb-2">Instructions:</h3>
        <ol className="list-decimal list-inside space-y-1 text-sm">
          <li>Type something in the input field above</li>
          <li>Try clicking one of the navigation links</li>
          <li>You should see an unsaved changes dialog</li>
          <li>Test the Save, Discard, and Cancel options</li>
        </ol>
      </div>
    </div>
  );
}
