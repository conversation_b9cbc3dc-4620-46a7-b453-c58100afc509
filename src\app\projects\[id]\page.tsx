'use client';

import { useState, useEffect } from 'react';
import { useRouter, useParams } from 'next/navigation';
import Link from 'next/link';
import { getProjectById, updateProject, deleteProject } from '@/services/project-service';
import { toast } from 'sonner';
import { useUnsavedChanges } from '@/contexts/unsaved-changes-context';

// Animation classes are defined in the global CSS
import { NavSidebar } from '@/components/nav-sidebar';
import { AppHeader } from '@/components/app-header';
import { NewProjectButton } from '@/components/new-project-button';
import { Button } from "@/components/ui/button";
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Building, Calendar, Users, FileText, CheckCircle2, Save, X, AlertTriangle, Archive, Trash2 } from 'lucide-react';

// Project data is fetched from Supabase

export default function ProjectDetailsPage() {
  const params = useParams();
  const projectId = params.id as string; // Use string ID for UUID compatibility

  // Router for navigation
  const router = useRouter();

  // Global unsaved changes context
  const { setHasUnsavedChanges, registerSaveHandler, unregisterSaveHandler } = useUnsavedChanges();

  // State for loading and error handling
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // State for project data
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const [project, setProject] = useState<any>(null);

  // Fetch project data from API
  useEffect(() => {
    async function fetchProject() {
      try {
        setLoading(true);
        const { data, error } = await getProjectById(projectId);

        if (error) {
          console.error(`Error fetching project with id ${projectId}:`, error);
          setError('Failed to load project details. Please try again later.');
          setProject(null);
        } else if (data) {
          setProject(data);
          setError(null);
        } else {
          // If no data but also no error, project doesn't exist
          setProject(null);
          setError('Project not found');
        }
      } catch (err) {
        console.error('Unexpected error:', err);
        setError('An unexpected error occurred. Please try again later.');
        setProject(null);
      } finally {
        setLoading(false);
      }
    }

    if (projectId) {
      fetchProject();
    }
  }, [projectId]);

  // State for tracking edit mode
  const [isEditing] = useState(true);

  // State to track if changes have been made
  const [hasChanges, setHasChanges] = useState(false);

  // State for save confirmation dialog
  const [showSaveDialog, setShowSaveDialog] = useState(false);
  const [pendingAction, setPendingAction] = useState<string | null>(null);

  // State for delete and archive operations
  const [isDeleting, setIsDeleting] = useState(false);
  const [isArchiving, setIsArchiving] = useState(false);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);

  // Handle delete project
  const handleDeleteProject = async () => {
    if (isDeleting) return;

    setIsDeleting(true);
    try {
      const { success, error } = await deleteProject(projectId);

      if (success) {
        toast.success('Project deleted successfully');

        // Use immediate navigation for better user experience
        window.location.href = '/projects';
      } else {
        toast.error(`Failed to delete project: ${error?.message || 'Unknown error'}`);
        setIsDeleting(false);
      }
    } catch (err) {
      toast.error('An error occurred while deleting the project');
      console.error('Error deleting project:', err);
      setIsDeleting(false);
    }
    setShowDeleteConfirm(false);
  };

  // Handle archive project
  const handleArchiveProject = async () => {
    if (isArchiving) return;

    setIsArchiving(true);
    try {
      const updatedProject = {
        ...project,
        status: 'Archived'
      };

      const { data, error } = await updateProject(projectId, updatedProject);

      if (data) {
        toast.success('Project archived successfully');

        // Update local state immediately
        setProjectStatus('Archived');
        setProject({
          ...project,
          status: 'Archived'
        });

        setIsArchiving(false);
        setHasChanges(false);
      } else {
        toast.error(`Failed to archive project: ${error?.message || 'Unknown error'}`);
        setIsArchiving(false);
      }
    } catch (err) {
      toast.error('An error occurred while archiving the project');
      console.error('Error archiving project:', err);
      setIsArchiving(false);
    }
  };

  // Navigation warning is now handled by the global UnsavedChangesProvider

  // Original project data for comparison
  const [originalData, setOriginalData] = useState({
    name: '',
    location: '',
    type: '',
    status: 'Planning',
    description: '',
    buildingConsent: '',
    resourceConsent: '',
    worksOver: 'Not Applicable',
    worksOverNumber: '',
    startDate: '',
    completionDate: '',
    estimatedBudget: '',
    actualCost: '',
    salePrice: '',
    clientName: '',
    projectManager: ''
  });

  // Form state
  const [projectName, setProjectName] = useState('');
  const [projectLocation, setProjectLocation] = useState('');
  const [projectType, setProjectType] = useState('');
  const [projectStatus, setProjectStatus] = useState('Planning');
  const [projectDescription, setProjectDescription] = useState('');

  // Building & Compliance state
  const [buildingConsent, setBuildingConsent] = useState('');
  const [resourceConsent, setResourceConsent] = useState('');
  const [worksOver, setWorksOver] = useState('Not Applicable');
  const [worksOverNumber, setWorksOverNumber] = useState('');

  // Timeline & Budget state
  const [startDate, setStartDate] = useState('');
  const [completionDate, setCompletionDate] = useState('');
  const [estimatedBudget, setEstimatedBudget] = useState('');
  const [actualCost, setActualCost] = useState('');
  const [salePrice, setSalePrice] = useState('');
  const [lender, setLender] = useState('');

  // Team state
  const [clientName, setClientName] = useState('');
  const [projectManager, setProjectManager] = useState('');

  // Update form state when project data changes
  useEffect(() => {
    if (project) {
      console.log('Updating form state with project data:', project);

      // Update form state
      setProjectName(project.name || '');
      setProjectLocation(project.location || '');
      setProjectType(project.type || '');
      setProjectStatus(project.status || 'Planning');
      setProjectDescription(project.description || '');

      // Update building & compliance state
      setBuildingConsent(project.building_consent || '');
      setResourceConsent(project.resource_consent || '');
      setWorksOver(project.works_over || 'Not Applicable');
      setWorksOverNumber(project.works_over_number || '');

      // Update timeline & budget state
      setStartDate(project.start_date || '');
      setCompletionDate(project.completion_date || '');
      setEstimatedBudget(project.estimated_budget || '');
      setActualCost(project.actual_cost || '');
      setSalePrice(project.sale_price || '');

      // Update team state
      setClientName(project.client_name || '');
      setProjectManager(project.project_manager || '');

      // Update original data for comparison
      setOriginalData({
        name: project.name || '',
        location: project.location || '',
        type: project.type || '',
        status: project.status || 'Planning',
        description: project.description || '',
        buildingConsent: project.building_consent || '',
        resourceConsent: project.resource_consent || '',
        worksOver: project.works_over || 'Not Applicable',
        worksOverNumber: project.works_over_number || '',
        startDate: project.start_date || '',
        completionDate: project.completion_date || '',
        estimatedBudget: project.estimated_budget || '',
        actualCost: project.actual_cost || '',
        salePrice: project.sale_price || '',
        clientName: project.client_name || '',
        projectManager: project.project_manager || ''
      });
    }
  }, [project]);

  // Register save handler with global context
  useEffect(() => {
    registerSaveHandler(handleSave);
    return () => unregisterSaveHandler();
  }, [registerSaveHandler, unregisterSaveHandler]);

  // Function to check if any changes have been made
  const checkForChanges = () => {
    const currentData = {
      name: projectName,
      location: projectLocation,
      type: projectType,
      status: projectStatus,
      description: projectDescription,
      buildingConsent,
      resourceConsent,
      worksOver,
      worksOverNumber,
      startDate,
      completionDate,
      estimatedBudget,
      actualCost,
      salePrice,
      clientName,
      projectManager
    };

    // Compare current data with original data
    const changed = Object.keys(currentData).some(key =>
      currentData[key as keyof typeof currentData] !== originalData[key as keyof typeof originalData]
    );

    setHasChanges(changed);
    setHasUnsavedChanges(changed); // Update global context
  };

  // Update handler for all form fields
  const handleFieldChange = (field: string, value: string) => {
    console.log(`Field changed: ${field} = ${value}`);

    switch (field) {
      case 'name': setProjectName(value); break;
      case 'location': setProjectLocation(value); break;
      case 'type': setProjectType(value); break;
      case 'status': setProjectStatus(value); break;
      case 'description': setProjectDescription(value); break;
      case 'buildingConsent': setBuildingConsent(value); break;
      case 'resourceConsent': setResourceConsent(value); break;
      case 'worksOver': setWorksOver(value); break;
      case 'worksOverNumber': setWorksOverNumber(value); break;
      case 'startDate': setStartDate(value); break;
      case 'completionDate': setCompletionDate(value); break;
      case 'estimatedBudget': setEstimatedBudget(value); break;
      case 'actualCost': setActualCost(value); break;
      case 'salePrice': setSalePrice(value); break;
      case 'clientName': setClientName(value); break;
      case 'projectManager': setProjectManager(value); break;
    }

    // Immediately set hasChanges to true when any field changes
    setHasChanges(true);
    setHasUnsavedChanges(true); // Update global context immediately
    console.log('Changes detected, save button should appear');

    // Also check for changes after updating a field (for more detailed comparison)
    setTimeout(checkForChanges, 0);
  };

  // Navigation is handled by the router

  // Handle save action
  const handleSave = async () => {
    try {
      console.log('Saving project data...');

      // Create project data object with snake_case property names for Supabase
      const projectData = {
        name: projectName,
        location: projectLocation,
        type: projectType,
        status: projectStatus,
        description: projectDescription,
        building_consent: buildingConsent,
        resource_consent: resourceConsent,
        works_over: worksOver,
        works_over_number: worksOverNumber,
        start_date: startDate,
        completion_date: completionDate,
        estimated_budget: estimatedBudget,
        actual_cost: actualCost,
        sale_price: salePrice,
        client_name: clientName,
        project_manager: projectManager,
        updated_at: new Date().toISOString()
      };

      console.log('Updating project with data:', projectData);

      // Save to Supabase
      const { data, error } = await updateProject(projectId, projectData);

      if (error) {
        console.error('Error updating project:', error);
        // Show error message to user
        console.log(`Failed to save changes: ${error.message || 'Unknown error'}`);
        return;
      }

      console.log('Project updated successfully:', data);

      // Update original data to match current data
      setOriginalData({
        name: projectName,
        location: projectLocation,
        type: projectType,
        status: projectStatus,
        description: projectDescription,
        buildingConsent,
        resourceConsent,
        worksOver,
        worksOverNumber,
        startDate,
        completionDate,
        estimatedBudget,
        actualCost,
        salePrice,
        clientName,
        projectManager
      });

      // Reset changes flag
      setHasChanges(false);
      setHasUnsavedChanges(false); // Update global context

      // If there was a pending action, execute it now
      if (pendingAction) {
        router.push(pendingAction);
        setPendingAction(null);
      }

      // Close the dialog if it was open
      setShowSaveDialog(false);

      // Show success message
      toast.success('Project saved successfully!');
      console.log('Project saved successfully!');
    } catch (err) {
      console.error('Unexpected error saving project:', err);
      console.log(`An unexpected error occurred: ${err instanceof Error ? err.message : 'Unknown error'}`);
    }
  };

  // Handle discard changes
  const handleDiscardChanges = () => {
    // Reset form fields to original values
    setProjectName(originalData.name);
    setProjectLocation(originalData.location);
    setProjectType(originalData.type);
    setProjectStatus(originalData.status);
    setProjectDescription(originalData.description);
    setBuildingConsent(originalData.buildingConsent);
    setResourceConsent(originalData.resourceConsent);
    setWorksOver(originalData.worksOver);
    setWorksOverNumber(originalData.worksOverNumber);
    setStartDate(originalData.startDate);
    setCompletionDate(originalData.completionDate);
    setEstimatedBudget(originalData.estimatedBudget);
    setActualCost(originalData.actualCost);
    setSalePrice(originalData.salePrice);
    setClientName(originalData.clientName);
    setProjectManager(originalData.projectManager);

    // Reset changes flag
    setHasChanges(false);
    setHasUnsavedChanges(false); // Update global context

    // If there was a pending action, execute it now
    if (pendingAction) {
      router.push(pendingAction);
      setPendingAction(null);
    }

    // Close the dialog
    setShowSaveDialog(false);
  };

  if (loading) {
    return (
      <div className="flex flex-col h-screen">
        <AppHeader />
        <div className="flex">
          <NavSidebar />
          <div className="ml-60 mt-[72px] p-8 w-full h-screen overflow-y-auto">
            <div className="text-center">
              <h1 className="text-2xl font-bold text-blue-600">Loading Project...</h1>
              <p className="mt-4">Please wait while we load the project details.</p>
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (error || !project) {
    return (
      <div className="flex flex-col h-screen">
        <AppHeader />
        <div className="flex">
          <NavSidebar />
          <div className="ml-60 mt-[72px] p-8 w-full h-screen overflow-y-auto">
            <div className="text-center">
              <h1 className="text-2xl font-bold text-red-600">Project Not Found</h1>
              <p className="mt-4">{error || "The project you're looking for doesn't exist."}</p>
              <p className="mt-2 text-sm text-gray-500">
                This could be because:
                <ul className="mt-2 list-disc list-inside">
                  <li>The project ID in the URL is incorrect</li>
                  <li>The project has been deleted</li>
                  <li>There&apos;s an issue with the database connection</li>
                </ul>
              </p>
              <Link href="/projects" className="mt-6 inline-block text-blue-600 hover:underline">
                Return to Projects
              </Link>
              <p className="mt-4 text-sm">
                Having trouble? Check the{' '}
                <Link href="/projects" className="text-blue-600 underline hover:text-blue-800">
                  projects page
                </Link>{' '}
                to see all available projects.
              </p>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="flex flex-col h-screen">
      <AppHeader />
      <div className="flex">
        <NavSidebar />
          {/* Main content area with padding for fixed header and sidebar */}
          <div className="ml-60 mt-[72px] p-6 pb-20 w-full h-screen overflow-y-auto">
            <div className="mb-4 flex justify-between items-center">
              <div className="flex items-center gap-3">
                <h1 className="text-xl font-bold text-blue-700">{project.name}</h1>
                <div className={`px-2 py-1 rounded-lg text-xs font-medium ${
                  project.status === 'In Progress'
                    ? 'bg-blue-100 text-blue-800'
                    : project.status === 'Planning'
                    ? 'bg-yellow-100 text-yellow-800'
                    : project.status === 'On Hold'
                    ? 'bg-orange-100 text-orange-800'
                    : project.status === 'For Sale'
                    ? 'bg-green-100 text-green-800'
                    : project.status === 'Completed'
                    ? 'bg-emerald-100 text-emerald-800'
                    : 'bg-gray-100 text-gray-800'
                }`}>
                  {project.status}
                </div>
              </div>
              <div className="flex items-center gap-3">
                <Button
                  variant="outline"
                  className="border-gray-300 text-gray-700 hover:bg-gray-50"
                  onClick={handleArchiveProject}
                  disabled={isArchiving || project.status === 'Archived'}
                >
                  <Archive className="h-4 w-4 mr-2" />
                  {isArchiving ? 'Archiving...' : 'Archive'}
                </Button>
                <Button
                  variant="outline"
                  className="border-red-200 text-red-600 hover:bg-red-50"
                  onClick={() => setShowDeleteConfirm(true)}
                  disabled={isDeleting}
                >
                  <Trash2 className="h-4 w-4 mr-2" />
                  {isDeleting ? 'Deleting...' : 'Delete'}
                </Button>
                <div onClick={() => hasChanges ? setShowSaveDialog(true) : null}>
                  <NewProjectButton />
                </div>
              </div>
            </div>

            {/* Save Confirmation Dialog */}
            {showSaveDialog && (
              <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
                <div className="bg-white rounded-lg shadow-xl p-6 max-w-md w-full mx-4 animate-fade-in-scale">
                  <div className="flex items-start mb-4">
                    <div className="bg-amber-100 p-2 rounded-full mr-4">
                      <AlertTriangle className="h-6 w-6 text-amber-600" />
                    </div>
                    <div>
                      <h3 className="text-lg font-semibold text-gray-900">Unsaved Changes</h3>
                      <p className="text-gray-600 mt-1">
                        You have unsaved changes. Would you like to save them before leaving?
                      </p>
                    </div>
                  </div>
                  <div className="flex justify-end gap-3 mt-6">
                    <Button
                      variant="outline"
                      className="border-gray-300"
                      onClick={() => setShowSaveDialog(false)}
                    >
                      <X className="h-4 w-4 mr-1" />
                      Cancel
                    </Button>
                    <Button
                      variant="outline"
                      className="border-gray-300"
                      onClick={handleDiscardChanges}
                    >
                      Discard Changes
                    </Button>
                    <Button
                      className="bg-blue-600 hover:bg-blue-700"
                      onClick={handleSave}
                    >
                      <Save className="h-4 w-4 mr-1" />
                      Save Changes
                    </Button>
                  </div>
                </div>
              </div>
            )}

            {/* Delete Confirmation Dialog */}
            {showDeleteConfirm && (
              <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
                <div className="bg-white rounded-lg shadow-xl p-6 max-w-md w-full mx-4 animate-fade-in-scale">
                  <div className="flex items-start mb-4">
                    <div className="bg-red-100 p-2 rounded-full mr-4">
                      <Trash2 className="h-6 w-6 text-red-600" />
                    </div>
                    <div>
                      <h3 className="text-lg font-semibold text-gray-900">Delete Project</h3>
                      <p className="text-gray-600 mt-1">
                        Are you sure you want to delete this project? This action cannot be undone.
                      </p>
                    </div>
                  </div>
                  <div className="flex justify-end gap-3 mt-6">
                    <Button
                      variant="outline"
                      className="border-gray-300"
                      onClick={() => setShowDeleteConfirm(false)}
                    >
                      <X className="h-4 w-4 mr-1" />
                      Cancel
                    </Button>
                    <Button
                      className="bg-red-600 hover:bg-red-700 text-white"
                      onClick={handleDeleteProject}
                      disabled={isDeleting}
                    >
                      <Trash2 className="h-4 w-4 mr-1" />
                      {isDeleting ? 'Deleting...' : 'Delete Project'}
                    </Button>
                  </div>
                </div>
              </div>
            )}



            {/* Floating Save Button - Only appears when changes are made */}
            {hasChanges && (
              <div className="fixed bottom-8 right-8 z-50 animate-fade-in">
                <Button
                  className="bg-blue-600 hover:bg-blue-700 shadow-lg px-6 py-6 rounded-full flex items-center gap-2 transition-all duration-300 hover:scale-105"
                  onClick={handleSave}
                >
                  <CheckCircle2 className="h-5 w-5" />
                  <span className="font-medium">Save Changes</span>
                </Button>
              </div>
            )}

            {/* Project Content - Vertical Layout */}
            <div className="space-y-6">
            {/* Project Overview */}
            <div className="bg-white rounded-lg shadow-md overflow-hidden border border-slate-400">
              <div className="flex items-center gap-2 px-4 py-3 bg-blue-50 border-b border-blue-100">
                <FileText className="h-4 w-4 text-blue-600" />
                <h2 className="font-semibold text-blue-800 text-sm">Project Overview</h2>
              </div>

              <div className="p-4 space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="project-name" className="text-gray-700">Project Name</Label>
                    <Input
                      id="project-name"
                      value={projectName}
                      onChange={(e) => handleFieldChange('name', e.target.value)}
                      placeholder="Enter project name"
                      className="mt-1.5"
                      disabled={!isEditing}
                    />
                  </div>
                  <div>
                    <Label htmlFor="project-location" className="text-gray-700">Location</Label>
                    <Input
                      id="project-location"
                      value={projectLocation}
                      onChange={(e) => handleFieldChange('location', e.target.value)}
                      placeholder="Enter project location"
                      className="mt-1.5"
                      disabled={!isEditing}
                    />
                  </div>
                  <div>
                    <Label htmlFor="project-type" className="text-gray-700">Project Type</Label>
                    <Select
                      value={projectType}
                      onValueChange={(value) => handleFieldChange('type', value)}
                      disabled={!isEditing}
                    >
                      <SelectTrigger id="project-type" className="mt-1.5">
                        <SelectValue placeholder="Select project type" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="Standalone">Standalone</SelectItem>
                        <SelectItem value="Terraced House">Terraced House</SelectItem>
                        <SelectItem value="Apartments">Apartments</SelectItem>
                        <SelectItem value="Other">Other</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div>
                    <Label htmlFor="project-status" className="text-gray-700">Status</Label>
                    <Select
                      value={projectStatus}
                      onValueChange={(value) => handleFieldChange('status', value)}
                      disabled={!isEditing}
                    >
                      <SelectTrigger id="project-status" className="mt-1.5">
                        <SelectValue placeholder="Select status" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="Planning">Planning</SelectItem>
                        <SelectItem value="In Progress">In Progress</SelectItem>
                        <SelectItem value="Completed">Completed</SelectItem>
                        <SelectItem value="On Hold">On Hold</SelectItem>
                        <SelectItem value="For Sale">For Sale</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                <div>
                  <Label htmlFor="project-description" className="text-gray-700">Description</Label>
                  <Textarea
                    id="project-description"
                    value={projectDescription}
                    onChange={(e) => handleFieldChange('description', e.target.value)}
                    placeholder="Enter project description"
                    className="mt-1.5 min-h-[120px]"
                    disabled={!isEditing}
                  />
                </div>
              </div>
            </div>

            {/* Building & Compliance */}
            <div className="bg-white rounded-lg shadow-md overflow-hidden border border-slate-400">
              <div className="flex items-center gap-2 px-4 py-3 bg-blue-50 border-b border-blue-100">
                <Building className="h-4 w-4 text-blue-600" />
                <h2 className="font-semibold text-blue-800 text-sm">Building & Compliance</h2>
              </div>

              <div className="p-4 space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="building-consent" className="text-gray-700">Building Consent</Label>
                    <Input
                      id="building-consent"
                      value={buildingConsent}
                      onChange={(e) => handleFieldChange('buildingConsent', e.target.value)}
                      placeholder="Enter building consent details"
                      className="mt-1.5"
                      disabled={!isEditing}
                    />
                  </div>
                  <div>
                    <Label htmlFor="resource-consent" className="text-gray-700">Resource Consent</Label>
                    <Input
                      id="resource-consent"
                      value={resourceConsent}
                      onChange={(e) => handleFieldChange('resourceConsent', e.target.value)}
                      placeholder="Enter resource consent details"
                      className="mt-1.5"
                      disabled={!isEditing}
                    />
                  </div>
                  <div>
                    <Label htmlFor="works-over" className="text-gray-700">Works Over</Label>
                    <Select
                      value={worksOver}
                      onValueChange={(value) => handleFieldChange('worksOver', value)}
                      disabled={!isEditing}
                    >
                      <SelectTrigger id="works-over" className="mt-1.5">
                        <SelectValue placeholder="Select option" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="Not Applicable">Not Applicable</SelectItem>
                        <SelectItem value="Applicable">Applicable</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  {worksOver === 'Applicable' && (
                    <div>
                      <Label htmlFor="works-over-number" className="text-gray-700">Works Over Number</Label>
                      <Input
                        id="works-over-number"
                        value={worksOverNumber}
                        onChange={(e) => handleFieldChange('worksOverNumber', e.target.value)}
                        placeholder="Enter works over number"
                        className="mt-1.5"
                        disabled={!isEditing}
                      />
                    </div>
                  )}
                </div>
              </div>
            </div>

            {/* Timeline & Budget */}
            <div className="bg-white rounded-lg shadow-md overflow-hidden border border-slate-400">
              <div className="flex items-center gap-2 px-4 py-3 bg-blue-50 border-b border-blue-100">
                <Calendar className="h-4 w-4 text-blue-600" />
                <h2 className="font-semibold text-blue-800 text-sm">Timeline & Budget</h2>
              </div>

              <div className="p-4 space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="start-date" className="text-gray-700">Start Date</Label>
                    <Input
                      id="start-date"
                      type="date"
                      value={startDate}
                      onChange={(e) => handleFieldChange('startDate', e.target.value)}
                      className="mt-1.5"
                      disabled={!isEditing}
                    />
                  </div>
                  <div>
                    <Label htmlFor="completion-date" className="text-gray-700">Completion Date</Label>
                    <Input
                      id="completion-date"
                      type="date"
                      value={completionDate}
                      onChange={(e) => handleFieldChange('completionDate', e.target.value)}
                      className="mt-1.5"
                      disabled={!isEditing}
                    />
                  </div>
                  <div>
                    <Label htmlFor="estimated-budget" className="text-gray-700">Estimated Budget</Label>
                    <Input
                      id="estimated-budget"
                      value={estimatedBudget}
                      onChange={(e) => handleFieldChange('estimatedBudget', e.target.value)}
                      placeholder="Enter estimated budget"
                      className="mt-1.5"
                      disabled={!isEditing}
                    />
                  </div>
                  <div>
                    <Label htmlFor="actual-cost" className="text-gray-700">Actual Cost</Label>
                    <Input
                      id="actual-cost"
                      value={actualCost}
                      onChange={(e) => handleFieldChange('actualCost', e.target.value)}
                      placeholder="Enter actual cost"
                      className="mt-1.5"
                      disabled={!isEditing}
                    />
                  </div>
                  <div>
                    <Label htmlFor="sale-price" className="text-gray-700">Sale Price</Label>
                    <Input
                      id="sale-price"
                      value={salePrice}
                      onChange={(e) => handleFieldChange('salePrice', e.target.value)}
                      placeholder="e.g. $500,000"
                      className="mt-1.5"
                      disabled={!isEditing}
                    />
                  </div>
                </div>
              </div>
            </div>

            {/* Team */}
            <div className="bg-white rounded-lg shadow-md overflow-hidden border border-slate-400">
              <div className="flex items-center gap-2 px-4 py-3 bg-blue-50 border-b border-blue-100">
                <Users className="h-4 w-4 text-blue-600" />
                <h2 className="font-semibold text-blue-800 text-sm">Team</h2>
              </div>

              <div className="p-4 space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="client-name" className="text-gray-700">Client Name</Label>
                    <Input
                      id="client-name"
                      value={clientName}
                      onChange={(e) => handleFieldChange('clientName', e.target.value)}
                      placeholder="Enter client name"
                      className="mt-1.5"
                      disabled={!isEditing}
                    />
                  </div>
                  <div>
                    <Label htmlFor="project-manager" className="text-gray-700">Project Manager</Label>
                    <Input
                      id="project-manager"
                      value={projectManager}
                      onChange={(e) => handleFieldChange('projectManager', e.target.value)}
                      placeholder="Enter project manager name"
                      className="mt-1.5"
                      disabled={!isEditing}
                    />
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
