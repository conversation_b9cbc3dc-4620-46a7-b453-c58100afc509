'use client';

import React, { createContext, useContext, useState, useCallback, useEffect } from 'react';
import { useRouter, usePathname } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { AlertTriangle, Save, X } from 'lucide-react';

interface UnsavedChangesContextType {
  hasUnsavedChanges: boolean;
  setHasUnsavedChanges: (hasChanges: boolean) => void;
  registerSaveHandler: (handler: () => Promise<void> | void) => void;
  unregisterSaveHandler: () => void;
  checkUnsavedChanges: () => boolean;
}

const UnsavedChangesContext = createContext<UnsavedChangesContextType | undefined>(undefined);

export function useUnsavedChanges() {
  const context = useContext(UnsavedChangesContext);
  if (context === undefined) {
    throw new Error('useUnsavedChanges must be used within an UnsavedChangesProvider');
  }
  return context;
}

interface UnsavedChangesProviderProps {
  children: React.ReactNode;
}

export function UnsavedChangesProvider({ children }: UnsavedChangesProviderProps) {
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);
  const [saveHandler, setSaveHandler] = useState<(() => Promise<void> | void) | null>(null);
  const [showDialog, setShowDialog] = useState(false);
  const [pendingNavigation, setPendingNavigation] = useState<string | null>(null);
  const router = useRouter();
  const pathname = usePathname();

  const registerSaveHandler = useCallback((handler: () => Promise<void> | void) => {
    setSaveHandler(() => handler);
  }, []);

  const unregisterSaveHandler = useCallback(() => {
    setSaveHandler(null);
  }, []);

  const checkUnsavedChanges = useCallback(() => {
    return hasUnsavedChanges;
  }, [hasUnsavedChanges]);

  // Handle browser navigation (back/forward/refresh)
  useEffect(() => {
    const handleBeforeUnload = (e: BeforeUnloadEvent) => {
      if (hasUnsavedChanges) {
        e.preventDefault();
        const message = 'You have unsaved changes. Are you sure you want to leave?';
        e.returnValue = message;
        return message;
      }
    };

    window.addEventListener('beforeunload', handleBeforeUnload);
    return () => window.removeEventListener('beforeunload', handleBeforeUnload);
  }, [hasUnsavedChanges]);

  // Handle programmatic navigation
  useEffect(() => {
    if (hasUnsavedChanges) {
      // Override the router push method to show confirmation dialog
      const originalPush = router.push;
      const originalReplace = router.replace;
      const originalBack = router.back;

      router.push = (href: string, options?: any) => {
        if (hasUnsavedChanges && href !== pathname) {
          setPendingNavigation(href);
          setShowDialog(true);
          return Promise.resolve(true);
        }
        return originalPush(href, options);
      };

      router.replace = (href: string, options?: any) => {
        if (hasUnsavedChanges && href !== pathname) {
          setPendingNavigation(href);
          setShowDialog(true);
          return Promise.resolve(true);
        }
        return originalReplace(href, options);
      };

      router.back = () => {
        if (hasUnsavedChanges) {
          setPendingNavigation('back');
          setShowDialog(true);
          return;
        }
        return originalBack();
      };

      return () => {
        router.push = originalPush;
        router.replace = originalReplace;
        router.back = originalBack;
      };
    }
  }, [hasUnsavedChanges, pathname, router]);

  const handleSave = async () => {
    if (saveHandler) {
      try {
        await saveHandler();
        setHasUnsavedChanges(false);
        setShowDialog(false);
        
        // Navigate to pending destination
        if (pendingNavigation) {
          if (pendingNavigation === 'back') {
            window.history.back();
          } else {
            window.location.href = pendingNavigation;
          }
          setPendingNavigation(null);
        }
      } catch (error) {
        console.error('Error saving changes:', error);
        // Keep dialog open if save fails
      }
    }
  };

  const handleDiscard = () => {
    setHasUnsavedChanges(false);
    setShowDialog(false);
    
    // Navigate to pending destination
    if (pendingNavigation) {
      if (pendingNavigation === 'back') {
        window.history.back();
      } else {
        window.location.href = pendingNavigation;
      }
      setPendingNavigation(null);
    }
  };

  const handleCancel = () => {
    setShowDialog(false);
    setPendingNavigation(null);
  };

  const contextValue: UnsavedChangesContextType = {
    hasUnsavedChanges,
    setHasUnsavedChanges,
    registerSaveHandler,
    unregisterSaveHandler,
    checkUnsavedChanges,
  };

  return (
    <UnsavedChangesContext.Provider value={contextValue}>
      {children}
      
      {/* Unsaved Changes Dialog */}
      {showDialog && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-[9999]">
          <div className="bg-white rounded-lg shadow-xl p-6 max-w-md w-full mx-4 animate-fade-in-scale">
            <div className="flex items-start mb-4">
              <div className="bg-amber-100 p-2 rounded-full mr-4">
                <AlertTriangle className="h-6 w-6 text-amber-600" />
              </div>
              <div>
                <h3 className="text-lg font-semibold text-gray-900">Unsaved Changes</h3>
                <p className="text-gray-600 mt-1">
                  You have unsaved changes. Would you like to save them before leaving?
                </p>
              </div>
            </div>
            <div className="flex justify-end gap-3 mt-6">
              <Button
                variant="outline"
                className="border-gray-300"
                onClick={handleCancel}
              >
                <X className="h-4 w-4 mr-1" />
                Cancel
              </Button>
              <Button
                variant="outline"
                className="border-gray-300"
                onClick={handleDiscard}
              >
                Discard Changes
              </Button>
              <Button
                className="bg-blue-600 hover:bg-blue-700"
                onClick={handleSave}
                disabled={!saveHandler}
              >
                <Save className="h-4 w-4 mr-1" />
                Save Changes
              </Button>
            </div>
          </div>
        </div>
      )}
    </UnsavedChangesContext.Provider>
  );
}
